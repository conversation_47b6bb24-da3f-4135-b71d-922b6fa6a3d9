import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext, simpledialog
import threading
import requests
import json
import os
import sys
import hashlib
import winreg
from pathlib import Path
import time
from datetime import datetime
import shutil

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from keyauth import api

class ConfigManager:
    """Manages persistent configuration storage in JSON format"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def get_default_config(self):
        """Returns default configuration structure"""
        return {
            "steam_path": "",
            "license_key_history": [],
            "admin_settings": {
                "password_hash": self.hash_password("admin123"),  # Default password
                "auto_detect_steam": True,
                "remember_license_keys": True,
                "max_history_entries": 50
            },
            "app_cache": {},
            "last_used": {
                "license_key": "",
                "app_info": {}
            }
        }
    
    def hash_password(self, password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def load_config(self):
        """Load configuration from JSON file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Ensure all required keys exist
                default_config = self.get_default_config()
                for key in default_config:
                    if key not in config:
                        config[key] = default_config[key]
                return config
            else:
                return self.get_default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
    
    def save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def get(self, key, default=None):
        """Get configuration value"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key, value):
        """Set configuration value"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()
    
    def add_license_history(self, license_key, app_info, success=True):
        """Add license key to history"""
        if not self.get("admin_settings.remember_license_keys", True):
            return
        
        history = self.config["license_key_history"]
        
        # Remove existing entry for this license key
        history = [h for h in history if h.get("key") != license_key]
        
        # Add new entry
        entry = {
            "key": license_key,
            "app_name": app_info.get("app_name", "Unknown"),
            "app_id": app_info.get("app_id", ""),
            "license_prefix": app_info.get("license_prefix", ""),
            "timestamp": datetime.now().isoformat(),
            "success": success
        }
        history.insert(0, entry)  # Add to beginning
        
        # Limit history size
        max_entries = self.get("admin_settings.max_history_entries", 50)
        if len(history) > max_entries:
            history = history[:max_entries]
        
        self.config["license_key_history"] = history
        self.save_config()
    
    def reset_all_data(self):
        """Reset all configuration data"""
        self.config = self.get_default_config()
        self.save_config()

class PasswordManager:
    """Manages admin password authentication"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def verify_password(self, password):
        """Verify admin password"""
        stored_hash = self.config_manager.get("admin_settings.password_hash")
        input_hash = self.config_manager.hash_password(password)
        return stored_hash == input_hash
    
    def change_password(self, new_password):
        """Change admin password"""
        new_hash = self.config_manager.hash_password(new_password)
        self.config_manager.set("admin_settings.password_hash", new_hash)
        return True

class SteamToolsGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Steam Tools Downloader v2")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Initialize configuration and password managers
        self.config_manager = ConfigManager()
        self.password_manager = PasswordManager(self.config_manager)
        
        # Initialize variables
        self.steam_path = self.config_manager.get("steam_path", "")
        self.keyauth_app = None
        self.license_key = ""
        self.app_info = {}
        self.is_admin_mode = False
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-Shift-A>', self.toggle_admin_mode)
        self.root.bind('<Control-Shift-C>', self.switch_to_customer_mode)
        
        # Make window focusable for keyboard shortcuts
        self.root.focus_set()

        # Create GUI based on mode
        self.create_customer_mode()

        # Initialize Keyauth after GUI is created (in a separate thread to avoid blocking)
        threading.Thread(target=self.init_keyauth, daemon=True).start()

        # Auto-detect Steam path if not set
        if not self.steam_path:
            self.auto_detect_steam_path()
    
    def toggle_admin_mode(self, event=None):
        """Toggle between customer and admin mode with password verification"""
        if self.is_admin_mode:
            self.switch_to_customer_mode()
        else:
            self.switch_to_admin_mode()
    
    def switch_to_admin_mode(self):
        """Switch to admin mode with password verification"""
        password = simpledialog.askstring("Admin Access", "Enter admin password:", show='*')
        if password and self.password_manager.verify_password(password):
            self.is_admin_mode = True
            self.clear_widgets()
            self.create_admin_mode()
            self.log_message("Switched to Admin Mode")
        elif password:  # Password was entered but incorrect
            messagebox.showerror("Access Denied", "Incorrect admin password")
    
    def switch_to_customer_mode(self, event=None):
        """Switch to customer mode"""
        self.is_admin_mode = False
        self.clear_widgets()
        self.create_customer_mode()
        if hasattr(self, 'log_message'):
            self.log_message("Switched to Customer Mode")
    
    def clear_widgets(self):
        """Clear all widgets from the window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def create_customer_mode(self):
        """Create simple customer interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Steam Tools Downloader", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 30))
        
        # Subtitle
        subtitle_label = ttk.Label(main_frame, text="Enter your license key to automatically download and install files", 
                                  font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # License Key Section
        ttk.Label(main_frame, text="License Key:", font=("Arial", 12)).grid(row=2, column=0, sticky=tk.W, pady=10)
        self.license_entry = ttk.Entry(main_frame, width=40, font=("Arial", 11))
        self.license_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=10, padx=(10, 0))
        
        # Auto-download button
        self.auto_download_btn = ttk.Button(main_frame, text="Download & Install", 
                                          command=self.auto_download, style="Accent.TButton")
        self.auto_download_btn.grid(row=3, column=0, columnspan=2, pady=20)
        
        # Progress Section
        self.progress_var = tk.StringVar(value="Ready")
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var, font=("Arial", 10))
        self.progress_label.grid(row=4, column=0, columnspan=2, pady=5)
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
        self.progress_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Status display (smaller)
        self.status_text = tk.Text(main_frame, height=8, width=60, font=("Consolas", 9))
        self.status_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(20, 0))
        
        # Admin mode hint
        hint_label = ttk.Label(main_frame, text="Press Ctrl+Shift+A for admin mode", 
                              font=("Arial", 8), foreground="gray")
        hint_label.grid(row=7, column=0, columnspan=2, pady=(10, 0))
        
        # Configure row weights for resizing
        main_frame.rowconfigure(6, weight=1)
        
        # Set focus to license entry
        self.license_entry.focus()
        
        # Bind Enter key to auto-download
        self.license_entry.bind('<Return>', lambda e: self.auto_download())

    def log_message(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}\n"

        # Use appropriate text widget based on mode
        try:
            if hasattr(self, 'status_text') and self.status_text.winfo_exists():  # Customer mode
                self.status_text.insert(tk.END, log_text)
                self.status_text.see(tk.END)
                self.root.update_idletasks()
            elif hasattr(self, 'log_text') and self.log_text.winfo_exists():  # Admin mode
                self.log_text.insert(tk.END, log_text)
                self.log_text.see(tk.END)
                self.root.update_idletasks()
            else:
                # Fallback: print to console if GUI not ready
                print(log_text.strip())
        except:
            # Fallback: print to console if any error
            print(log_text.strip())

    def auto_download(self):
        """Automatically validate license and download files"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return

        # Disable button during process
        self.auto_download_btn.config(state='disabled')
        self.progress_bar['value'] = 0
        self.progress_var.set("Validating license...")

        # Clear status
        self.status_text.delete(1.0, tk.END)

        # Run in separate thread
        threading.Thread(target=self._auto_download_thread, args=(license_key,), daemon=True).start()

    def _auto_download_thread(self, license_key):
        """Auto-download thread"""
        try:
            # Validate license
            self.root.after(0, lambda: self.log_message("Validating license key..."))

            if not self.keyauth_app:
                self.root.after(0, lambda: self.log_message("❌ Keyauth not initialized"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Keyauth not initialized. Please wait and try again."))
                return

            is_valid = self.keyauth_app.license(license_key)

            if not is_valid:
                self.root.after(0, lambda: self.log_message("❌ Invalid license key"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Invalid license key"))
                return

            self.root.after(0, lambda: self.log_message("✅ License key validated"))

            # Get app information
            main_data = self.keyauth_app.var("Main")
            if not main_data:
                self.root.after(0, lambda: self.log_message("❌ Failed to get app data"))
                return

            apps_data = json.loads(main_data)
            app_info = None

            # Find matching app
            for app in apps_data["apps"]:
                if license_key.startswith(app["license_prefix"]):
                    app_info = app
                    break

            if not app_info:
                self.root.after(0, lambda: self.log_message("❌ No matching app found"))
                return

            self.root.after(0, lambda: self.log_message(f"📱 Found app: {app_info['app_name']}"))

            # Ensure Steam path is available
            if not self.steam_path:
                self.root.after(0, lambda: self.auto_detect_steam_path())
                if not self.steam_path:
                    self.root.after(0, lambda: self.log_message("❌ Steam path not found"))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Steam installation not found. Please contact admin."))
                    return

            # Start download process
            self.root.after(0, lambda: self.progress_var.set("Downloading files..."))
            success = self._download_all_files(app_info)

            # Add to history
            self.config_manager.add_license_history(license_key, app_info, success)

            if success:
                self.root.after(0, lambda: self.progress_var.set("✅ Installation completed!"))
                self.root.after(0, lambda: self.log_message("🎉 All files downloaded and installed successfully!"))
                self.root.after(0, lambda: messagebox.showinfo("Success", "Files downloaded and installed successfully!"))
            else:
                self.root.after(0, lambda: self.progress_var.set("❌ Installation failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Some files failed to download. Please try again."))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Error: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"An error occurred: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.auto_download_btn.config(state='normal'))

    def init_keyauth(self):
        """Initialize Keyauth API"""
        try:
            self.keyauth_app = api(
                name="MainSteam",
                ownerid="1tGVnUKtzH",
                secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                version="1.0",
                hash_to_check=self.get_checksum()
            )
            self.log_message("Keyauth initialized successfully")
        except Exception as e:
            self.log_message(f"Failed to initialize Keyauth: {str(e)}")
            self.log_message("Please check your internet connection and try again.")
            # Don't exit, allow user to retry later

    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""

    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path from registry"""
        try:
            # Try to get Steam path from registry
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")
                    return True
        except:
            pass

        # Try alternative registry location
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "SteamPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")
                    return True
        except:
            pass

        # Try common installation paths
        common_paths = [
            r"C:\Program Files (x86)\Steam",
            r"C:\Program Files\Steam",
            r"D:\Steam",
            r"E:\Steam"
        ]

        for path in common_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "steam.exe")):
                self.steam_path = path
                self.config_manager.set("steam_path", path)
                self.log_message(f"Steam path found: {path}")
                return True

        self.log_message("Steam installation not found automatically")
        return False

    def _download_all_files(self, app_info):
        """Download all required files for the app"""
        try:
            total_files = 4
            current_file = 0
            success_count = 0

            # Create stplug-in directory
            stplug_in_path = os.path.join(self.steam_path, "config", "stplug-in")
            os.makedirs(stplug_in_path, exist_ok=True)
            self.log_message(f"📁 Created/verified directory: {stplug_in_path}")

            # Download hid.dll to Steam root
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading hid.dll ({current_file}/{total_files})"))
            steamtools_url = self.keyauth_app.var("SteamTools")
            if steamtools_url:
                hid_path = os.path.join(self.steam_path, "hid.dll")
                if self.download_file(steamtools_url, hid_path):
                    self.log_message("✅ hid.dll downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download hid.dll")

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download SteamTools-File1 (luapacka.exe)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading luapacka.exe ({current_file}/{total_files})"))
            file1_url = self.keyauth_app.var("SteamTools-File1")
            if file1_url:
                file1_path = os.path.join(stplug_in_path, "luapacka.exe")
                if self.download_file(file1_url, file1_path):
                    self.log_message("✅ luapacka.exe downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download luapacka.exe")

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download SteamTools-File2 (Steamtools.st)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading Steamtools.st ({current_file}/{total_files})"))
            file2_url = self.keyauth_app.var("SteamTools-File2")
            if file2_url:
                file2_path = os.path.join(stplug_in_path, "Steamtools.st")
                if self.download_file(file2_url, file2_path):
                    self.log_message("✅ Steamtools.st downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download Steamtools.st")

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download app-specific lua file
            current_file += 1
            app_name = app_info['app_name']
            self.root.after(0, lambda: self.progress_var.set(f"Downloading {app_name}.lua ({current_file}/{total_files})"))
            app_url = self.keyauth_app.var(app_name)
            if app_url:
                lua_filename = f"{app_info['app_id']}.lua"
                lua_path = os.path.join(stplug_in_path, lua_filename)
                if self.download_file(app_url, lua_path):
                    self.log_message(f"✅ {lua_filename} downloaded successfully")
                    success_count += 1
                else:
                    self.log_message(f"❌ Failed to download {lua_filename}")

            self.root.after(0, lambda: self.progress_bar.config(value=100))

            return success_count == total_files

        except Exception as e:
            self.log_message(f"❌ Download error: {str(e)}")
            return False

    def download_file(self, url, file_path):
        """Download a file from URL to local path"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            return True
        except Exception as e:
            self.log_message(f"❌ Download error for {os.path.basename(file_path)}: {str(e)}")
            return False

    def create_admin_mode(self):
        """Create comprehensive admin interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="Steam Tools Admin Panel",
                               font=("Arial", 16, "bold"), foreground="red")
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # License Key Section
        ttk.Label(main_frame, text="License Key:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.license_entry = ttk.Entry(main_frame, width=30)
        self.license_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))

        # Download button
        self.download_btn = ttk.Button(buttons_frame, text="Download Files", command=self.start_download)
        self.download_btn.grid(row=0, column=0, padx=5)

        # Steam path button
        ttk.Button(buttons_frame, text="Set Steam Path", command=self.browse_steam_path).grid(row=0, column=1, padx=5)

        # Auto-detect button
        ttk.Button(buttons_frame, text="Auto-detect Steam", command=self.auto_detect_steam_path).grid(row=0, column=2, padx=5)

        # Management buttons frame
        mgmt_frame = ttk.LabelFrame(main_frame, text="Management Functions", padding="10")
        mgmt_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))

        # Reset buttons
        ttk.Button(mgmt_frame, text="Reset All Data", command=self.reset_all_data).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(mgmt_frame, text="Reset stplug-in Folder", command=self.reset_stplug_folder).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(mgmt_frame, text="Delete All Files", command=self.delete_all_files).grid(row=0, column=2, padx=5, pady=5)

        # History and settings
        ttk.Button(mgmt_frame, text="View License History", command=self.view_license_history).grid(row=1, column=0, padx=5, pady=5)
        ttk.Button(mgmt_frame, text="Change Admin Password", command=self.change_admin_password).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(mgmt_frame, text="Customer Mode", command=self.switch_to_customer_mode).grid(row=1, column=2, padx=5, pady=5)

        # Steam path display
        ttk.Label(main_frame, text="Steam Path:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.steam_path_var = tk.StringVar(value=self.steam_path or "Not set")
        ttk.Label(main_frame, textvariable=self.steam_path_var, foreground="blue").grid(row=4, column=1, columnspan=2, sticky=tk.W, pady=5)

        # Progress Section
        self.progress_var = tk.StringVar(value="Ready")
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=5, column=0, columnspan=3, pady=5)

        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
        self.progress_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # Log Section
        ttk.Label(main_frame, text="Log:").grid(row=7, column=0, sticky=(tk.W, tk.N), pady=(10, 0))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # Configure row weights for resizing
        main_frame.rowconfigure(8, weight=1)

    def start_download(self):
        """Start download process (admin mode)"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return

        if not self.steam_path:
            messagebox.showerror("Error", "Please set Steam path first")
            return

        if not self.keyauth_app:
            messagebox.showerror("Error", "Keyauth not initialized. Please wait and try again.")
            return

        # Validate license
        try:
            is_valid = self.keyauth_app.license(license_key)
            if not is_valid:
                messagebox.showerror("Error", "Invalid license key")
                return
        except Exception as e:
            messagebox.showerror("Error", f"License validation failed: {str(e)}")
            return

        # Get app information
        try:
            main_data = self.keyauth_app.var("Main")
            if not main_data:
                messagebox.showerror("Error", "Failed to get app data")
                return

            apps_data = json.loads(main_data)
            app_info = None

            for app in apps_data["apps"]:
                if license_key.startswith(app["license_prefix"]):
                    app_info = app
                    break

            if not app_info:
                messagebox.showerror("Error", "No matching app found for this license key")
                return

            self.app_info = app_info
            self.log_message(f"Found app: {app_info['app_name']} (ID: {app_info['app_id']})")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get app information: {str(e)}")
            return

        # Start download in separate thread
        self.download_btn.config(state='disabled')
        self.progress_bar['value'] = 0
        threading.Thread(target=self._download_thread, daemon=True).start()

    def _download_thread(self):
        """Download files thread (admin mode)"""
        try:
            success = self._download_all_files(self.app_info)

            if success:
                self.root.after(0, lambda: self.progress_var.set("✅ Download completed!"))
                self.root.after(0, lambda: self.log_message("🎉 All downloads completed!"))
            else:
                self.root.after(0, lambda: self.progress_var.set("❌ Download failed"))
                self.root.after(0, lambda: self.log_message("❌ Some downloads failed"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Download error: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.download_btn.config(state='normal'))

    def browse_steam_path(self):
        """Browse for Steam installation path"""
        path = filedialog.askdirectory(title="Select Steam Installation Directory")
        if path and os.path.exists(os.path.join(path, "steam.exe")):
            self.steam_path = path
            self.config_manager.set("steam_path", path)
            self.steam_path_var.set(path)
            self.log_message(f"Steam path set to: {path}")
        elif path:
            messagebox.showerror("Error", "Invalid Steam directory. steam.exe not found.")

    def reset_all_data(self):
        """Reset all application data"""
        if messagebox.askyesno("Confirm Reset", "This will reset ALL application data including license history and settings. Continue?"):
            try:
                self.config_manager.reset_all_data()
                self.steam_path = ""
                if hasattr(self, 'steam_path_var'):
                    self.steam_path_var.set("Not set")
                self.log_message("✅ All data reset successfully")
                messagebox.showinfo("Success", "All data has been reset")
            except Exception as e:
                self.log_message(f"❌ Error resetting data: {str(e)}")
                messagebox.showerror("Error", f"Failed to reset data: {str(e)}")

    def reset_stplug_folder(self):
        """Reset/delete stplug-in folder"""
        if not self.steam_path:
            messagebox.showerror("Error", "Steam path not set")
            return

        stplug_path = os.path.join(self.steam_path, "config", "stplug-in")

        if messagebox.askyesno("Confirm Reset", f"This will delete the stplug-in folder:\n{stplug_path}\n\nContinue?"):
            try:
                if os.path.exists(stplug_path):
                    shutil.rmtree(stplug_path)
                    self.log_message(f"✅ Deleted stplug-in folder: {stplug_path}")
                    messagebox.showinfo("Success", "stplug-in folder deleted successfully")
                else:
                    self.log_message("ℹ️ stplug-in folder does not exist")
                    messagebox.showinfo("Info", "stplug-in folder does not exist")
            except Exception as e:
                self.log_message(f"❌ Error deleting stplug-in folder: {str(e)}")
                messagebox.showerror("Error", f"Failed to delete folder: {str(e)}")

    def delete_all_files(self):
        """Delete all hid.dll and stplug-in folder"""
        if not self.steam_path:
            messagebox.showerror("Error", "Steam path not set")
            return

        hid_path = os.path.join(self.steam_path, "hid.dll")
        stplug_path = os.path.join(self.steam_path, "config", "stplug-in")

        if messagebox.askyesno("Confirm Deletion", f"This will delete:\n- {hid_path}\n- {stplug_path}\n\nContinue?"):
            try:
                deleted_count = 0

                # Delete hid.dll
                if os.path.exists(hid_path):
                    os.remove(hid_path)
                    self.log_message(f"✅ Deleted: {hid_path}")
                    deleted_count += 1

                # Delete stplug-in folder
                if os.path.exists(stplug_path):
                    shutil.rmtree(stplug_path)
                    self.log_message(f"✅ Deleted: {stplug_path}")
                    deleted_count += 1

                if deleted_count > 0:
                    messagebox.showinfo("Success", f"Deleted {deleted_count} item(s) successfully")
                else:
                    messagebox.showinfo("Info", "No files found to delete")

            except Exception as e:
                self.log_message(f"❌ Error deleting files: {str(e)}")
                messagebox.showerror("Error", f"Failed to delete files: {str(e)}")

    def view_license_history(self):
        """View license key history"""
        history = self.config_manager.config.get("license_key_history", [])

        if not history:
            messagebox.showinfo("License History", "No license key history found")
            return

        # Create history window
        history_window = tk.Toplevel(self.root)
        history_window.title("License Key History")
        history_window.geometry("800x400")

        # Create treeview
        columns = ("Key", "App", "Date", "Status")
        tree = ttk.Treeview(history_window, columns=columns, show="headings")

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # Add data
        for entry in history:
            date_str = entry.get("timestamp", "Unknown")
            if "T" in date_str:
                date_str = date_str.split("T")[0]

            status = "✅ Success" if entry.get("success", False) else "❌ Failed"

            tree.insert("", "end", values=(
                entry.get("key", "")[:20] + "...",
                entry.get("app_name", "Unknown"),
                date_str,
                status
            ))

        tree.pack(fill="both", expand=True, padx=10, pady=10)

        # Clear history button
        def clear_history():
            if messagebox.askyesno("Confirm", "Clear all license history?"):
                self.config_manager.config["license_key_history"] = []
                self.config_manager.save_config()
                history_window.destroy()
                self.log_message("License history cleared")

        ttk.Button(history_window, text="Clear History", command=clear_history).pack(pady=10)

    def change_admin_password(self):
        """Change admin password"""
        current_password = simpledialog.askstring("Change Password", "Enter current admin password:", show='*')
        if not current_password or not self.password_manager.verify_password(current_password):
            messagebox.showerror("Error", "Incorrect current password")
            return

        new_password = simpledialog.askstring("Change Password", "Enter new admin password:", show='*')
        if not new_password:
            return

        confirm_password = simpledialog.askstring("Change Password", "Confirm new admin password:", show='*')
        if new_password != confirm_password:
            messagebox.showerror("Error", "Passwords do not match")
            return

        if len(new_password) < 4:
            messagebox.showerror("Error", "Password must be at least 4 characters long")
            return

        try:
            self.password_manager.change_password(new_password)
            self.log_message("Admin password changed successfully")
            messagebox.showinfo("Success", "Admin password changed successfully")
        except Exception as e:
            self.log_message(f"❌ Error changing password: {str(e)}")
            messagebox.showerror("Error", f"Failed to change password: {str(e)}")

def main():
    """Main application entry point"""
    root = tk.Tk()

    # Set window icon and properties
    root.iconbitmap() if hasattr(root, 'iconbitmap') else None

    # Create and run the application
    app = SteamToolsGUI(root)

    # Save configuration on exit
    def on_closing():
        app.config_manager.save_config()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
