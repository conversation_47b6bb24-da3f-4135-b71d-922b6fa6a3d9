# Steam Tools Downloader v2 - Packaging Guide

This folder contains scripts and configuration files to package the Steam Tools Downloader v2 GUI application into a single executable file for easy distribution.

## 📁 Files Overview

### Build Scripts
- **`build.bat`** - Main build script (recommended)
- **`build_exe.py`** - Python build script with detailed logging
- **`build_manual.bat`** - Alternative manual build script

### Configuration Files
- **`steam_tools_gui.spec`** - PyInstaller specification file
- **`version_info.txt`** - Windows executable version information
- **`build_requirements.txt`** - Build-time dependencies

### Documentation
- **`README_PACKAGING.md`** - This file

## 🚀 Quick Start

### Method 1: Automated Build (Recommended)
```bash
# Navigate to the pack folder
cd v2/pack

# Run the automated build script
build.bat
```

### Method 2: Python Build Script
```bash
# Navigate to the pack folder
cd v2/pack

# Run the Python build script directly
python build_exe.py
```

### Method 3: Manual Build
```bash
# Navigate to the pack folder
cd v2/pack

# Run the manual build script
build_manual.bat
```

## 📋 Prerequisites

### Required Software
- **Python 3.7+** installed and in PATH
- **pip** package manager
- **Internet connection** for downloading dependencies

### Automatic Installation
The build scripts will automatically install:
- PyInstaller (for creating executables)
- All required dependencies from `../requirements.txt`
- Additional build dependencies

## 🔧 Build Process

The build process performs the following steps:

1. **Dependency Check** - Verifies Python and required tools
2. **Dependency Installation** - Installs PyInstaller and app dependencies
3. **Environment Preparation** - Sets up build directories and copies files
4. **Executable Creation** - Uses PyInstaller to create single executable
5. **Distribution Packaging** - Creates ready-to-distribute folder
6. **Cleanup** - Removes temporary build files

## 📦 Output

After successful build, you'll find:

```
SteamToolsDownloader_v2_Distribution/
├── SteamToolsDownloader_v2.exe     # Main executable (15-25 MB)
├── Launch_SteamTools.bat           # User-friendly launcher
├── README.md                       # Application documentation
└── DISTRIBUTION_INFO.txt           # Distribution information
```

## 🎯 Distribution

The entire `SteamToolsDownloader_v2_Distribution` folder can be:
- Zipped and shared
- Copied to other computers
- Uploaded to file sharing services
- Distributed via USB drives

### End User Requirements
- **Windows 7 or later**
- **Internet connection** (for Keyauth validation and downloads)
- **No Python installation required**

## ⚙️ Advanced Configuration

### Customizing the Build

#### Icon
To add a custom icon, edit `steam_tools_gui.spec`:
```python
icon='path/to/your/icon.ico'  # Add your .ico file path
```

#### Version Information
Edit `version_info.txt` to customize:
- Company name
- File description
- Version numbers
- Copyright information

#### Hidden Imports
If you encounter import errors, add modules to `steam_tools_gui.spec`:
```python
hiddenimports=[
    'your_module_here',
    # ... existing modules
]
```

### Build Options

#### Console vs Windowed
- **Windowed** (default): No console window, GUI only
- **Console**: Shows console for debugging

Edit in `steam_tools_gui.spec`:
```python
console=False,  # Set to True for console mode
```

#### Compression
The build uses UPX compression by default for smaller file size:
```python
upx=True,  # Set to False to disable compression
```

## 🐛 Troubleshooting

### Common Issues

#### "PyInstaller not found"
**Solution**: Run `pip install pyinstaller`

#### "Module not found" errors
**Solution**: Add missing modules to `hiddenimports` in the spec file

#### "keyauth.py not found"
**Solution**: Ensure `../../src/keyauth.py` exists

#### Large executable size
**Solutions**:
- Ensure UPX is installed for compression
- Remove unnecessary hidden imports
- Use `--exclude-module` for unused modules

#### Build fails on antivirus
**Solution**: Temporarily disable antivirus or add build folder to exclusions

### Debug Mode
To debug build issues, edit `steam_tools_gui.spec`:
```python
debug=True,  # Enable debug mode
console=True,  # Show console for error messages
```

## 📊 File Size Expectations

- **Executable size**: 15-25 MB (with UPX compression)
- **Without compression**: 40-60 MB
- **Distribution folder**: 20-30 MB total

## 🔒 Security Notes

- The executable includes all dependencies
- Keyauth credentials are embedded (same as source)
- No additional security measures beyond source code
- Consider code obfuscation for production use

## 📝 Build Log

The Python build script (`build_exe.py`) provides detailed logging:
- Colored output for easy reading
- Step-by-step progress tracking
- Error reporting with suggestions
- Build time measurement

## 🆘 Support

If you encounter issues:

1. **Check the error messages** - they usually indicate the problem
2. **Verify prerequisites** - ensure Python and pip are working
3. **Try manual build** - use `build_manual.bat` for simpler process
4. **Check file paths** - ensure all source files exist
5. **Update dependencies** - run `pip install --upgrade pyinstaller`

## 📈 Performance Tips

- **Use SSD** for faster build times
- **Close unnecessary programs** during build
- **Ensure adequate disk space** (500MB+ free)
- **Use latest PyInstaller** for best compatibility
